// Test script for translation agent functionality
// This file can be used to test the translation agent implementation

import { translationService } from './services/translationService';
import { transcriptionStorageService } from './services/transcriptionStorageService';

// Mock test data
const mockSceneData = {
  analysisId: 'test-analysis-123',
  sceneStart: 0,
  sceneDuration: 10
};

const mockTranscriptionSegments = [
  {
    start: 0,
    end: 3,
    text: "Hello, welcome to our video editing platform.",
    words: []
  },
  {
    start: 3,
    end: 6,
    text: "Today we'll show you how to use AI agents.",
    words: []
  },
  {
    start: 6,
    end: 10,
    text: "Let's start with subtitle generation.",
    words: []
  }
];

const mockTranscriptionMetadata = {
  language: 'en',
  duration: 10,
  word_count: 15,
  segment_count: 3,
  transcription_method: 'assemblyai',
  created_at: Date.now()
};

// Test function
export async function testTranslationAgent() {
  console.log('🧪 Testing Translation Agent Implementation...');

  try {
    // Step 1: Store mock transcription
    console.log('📝 Storing mock transcription...');
    transcriptionStorageService.storeFullTranscription(
      mockSceneData.analysisId,
      mockTranscriptionSegments,
      mockTranscriptionMetadata
    );

    // Step 2: Test translation service
    console.log('🌐 Testing translation service...');
    const translationOptions = {
      targetLanguage: 'es',
      sourceLanguage: 'en',
      includeSubtitles: true,
      includeDubbedAudio: true,
      voiceType: 'neutral' as const,
      speechRate: 1.0
    };

    const result = await translationService.translateScene(
      mockSceneData,
      translationOptions
    );

    console.log('✅ Translation test results:', {
      originalSubtitles: result.originalSubtitles.length,
      translatedSubtitles: result.translatedSubtitles.length,
      hasDubbedAudio: !!result.dubbedAudioUrl,
      targetLanguage: result.targetLanguage,
      processingTime: result.processingTime
    });

    // Step 3: Test language selection
    console.log('🔧 Testing language selection...');
    const supportedLanguages = translationService.getSupportedLanguages();
    console.log('📋 Supported languages:', supportedLanguages.map(l => l.name).join(', '));

    // Step 4: Test different languages
    const testLanguages = ['fr', 'de', 'it'];
    for (const lang of testLanguages) {
      console.log(`🌍 Testing translation to ${lang}...`);
      const langResult = await translationService.translateScene(
        mockSceneData,
        { ...translationOptions, targetLanguage: lang }
      );
      console.log(`✅ ${lang.toUpperCase()} translation: ${langResult.translatedSubtitles.length} segments`);
    }

    console.log('🎉 All translation agent tests passed!');
    return true;

  } catch (error) {
    console.error('❌ Translation agent test failed:', error);
    return false;
  } finally {
    // Cleanup
    transcriptionStorageService.removeTranscription(mockSceneData.analysisId);
    console.log('🧹 Test cleanup completed');
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testTranslationAgent = testTranslationAgent;
  console.log('🔧 Translation agent test available as window.testTranslationAgent()');
}
