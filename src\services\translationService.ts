// src/services/translationService.ts
// Translation service using AssemblyAI transcription + Google Translate + Google TTS

import type { SubtitleCue } from '../components/SubtitleOverlay';
import { transcriptionStorageService } from './transcriptionStorageService';
import { getApiKey } from '../config/apiKeys';
import { subtitleGenerator } from './subtitleGenerator';

export interface TranslationOptions {
  targetLanguage: string;
  sourceLanguage?: string; // Auto-detect if not provided
  includeSubtitles: boolean;
  includeDubbedAudio: boolean;
  voiceType?: 'male' | 'female' | 'neutral';
  speechRate?: number; // 0.25 to 4.0
}

export interface TranslationResult {
  originalSubtitles: SubtitleCue[];
  translatedSubtitles: SubtitleCue[];
  dubbedAudioUrl?: string;
  dubbedAudioBlob?: Blob;
  sourceLanguage: string;
  targetLanguage: string;
  confidence: number;
  method: string;
  duration: number;
  processingTime: number;
}

// Google Translate API configuration
const GOOGLE_TRANSLATE_API_URL = 'https://translation.googleapis.com/language/translate/v2';
const GOOGLE_TTS_API_URL = 'https://texttospeech.googleapis.com/v1/text:synthesize';

// Language mappings for Google services
const LANGUAGE_CODES = {
  'english': 'en',
  'spanish': 'es',
  'french': 'fr',
  'german': 'de',
  'italian': 'it',
  'portuguese': 'pt',
  'russian': 'ru',
  'japanese': 'ja',
  'korean': 'ko',
  'chinese': 'zh',
  'arabic': 'ar',
  'hindi': 'hi'
};

const TTS_VOICE_CONFIGS = {
  'en': { name: 'en-US-Wavenet-D', gender: 'MALE' },
  'es': { name: 'es-ES-Wavenet-B', gender: 'MALE' },
  'fr': { name: 'fr-FR-Wavenet-B', gender: 'MALE' },
  'de': { name: 'de-DE-Wavenet-B', gender: 'MALE' },
  'it': { name: 'it-IT-Wavenet-A', gender: 'FEMALE' },
  'pt': { name: 'pt-BR-Wavenet-A', gender: 'FEMALE' },
  'ru': { name: 'ru-RU-Wavenet-A', gender: 'FEMALE' },
  'ja': { name: 'ja-JP-Wavenet-A', gender: 'FEMALE' },
  'ko': { name: 'ko-KR-Wavenet-A', gender: 'FEMALE' },
  'zh': { name: 'cmn-CN-Wavenet-A', gender: 'FEMALE' },
  'ar': { name: 'ar-XA-Wavenet-A', gender: 'FEMALE' },
  'hi': { name: 'hi-IN-Wavenet-A', gender: 'FEMALE' }
};

class TranslationService {
  private googleApiKey: string | null = null;

  constructor() {
    // Get Google Cloud API key for translation and TTS
    this.googleApiKey = getApiKey('google-cloud') || null;
    console.log('🔑 Google Cloud API key configured:', !!this.googleApiKey);
  }

  // Main translation method
  async translateScene(
    sceneData: { analysisId: string; sceneStart: number; sceneDuration: number },
    options: TranslationOptions,
    videoElement?: HTMLVideoElement
  ): Promise<TranslationResult> {
    const startTime = Date.now();

    try {
      console.log('🌐 Starting translation process...', { sceneData, options });

      // Step 1: Get original transcription (instant from pre-stored data)
      let originalSubtitles = await this.getOriginalTranscription(sceneData);

      // Step 1.5: If no transcription exists, generate subtitles first
      if (originalSubtitles.length === 0) {
        console.log('📝 No existing transcription found, generating subtitles first...');

        if (!videoElement) {
          throw new Error('No transcription available and no video element provided for subtitle generation');
        }

        try {
          const subtitleResult = await subtitleGenerator.generateSubtitles(videoElement, {
            language: 'en-US',
            confidence: 0.7
          }, {
            analysisId: sceneData.analysisId,
            sceneStart: sceneData.sceneStart,
            sceneDuration: sceneData.sceneDuration
          });

          originalSubtitles = subtitleResult.subtitles;
          console.log(`✅ Generated ${originalSubtitles.length} subtitles for translation`);
        } catch (subtitleError) {
          console.error('❌ Failed to generate subtitles for translation:', subtitleError);
          throw new Error('Failed to generate subtitles for translation. Please generate subtitles first.');
        }
      }

      if (originalSubtitles.length === 0) {
        throw new Error('No subtitles available for translation');
      }

      console.log(`📝 Found ${originalSubtitles.length} original subtitle segments`);

      // Step 2: Translate text
      let translatedSubtitles: SubtitleCue[] = [];
      if (options.includeSubtitles) {
        translatedSubtitles = await this.translateSubtitles(originalSubtitles, options);
        console.log(`🔄 Translated ${translatedSubtitles.length} subtitle segments`);
      }

      // Step 3: Generate dubbed audio
      let dubbedAudioUrl: string | undefined;
      let dubbedAudioBlob: Blob | undefined;
      if (options.includeDubbedAudio && translatedSubtitles.length > 0) {
        const audioResult = await this.generateDubbedAudio(translatedSubtitles, options);
        dubbedAudioUrl = audioResult.url;
        dubbedAudioBlob = audioResult.blob;
        console.log('🎤 Generated dubbed audio');
      }

      const processingTime = Date.now() - startTime;

      const result: TranslationResult = {
        originalSubtitles,
        translatedSubtitles,
        dubbedAudioUrl,
        dubbedAudioBlob,
        sourceLanguage: options.sourceLanguage || 'en',
        targetLanguage: options.targetLanguage,
        confidence: 0.9, // High confidence for API-based translation
        method: 'google-translate-tts',
        duration: sceneData.sceneDuration,
        processingTime
      };

      console.log('✅ Translation completed successfully', {
        processingTime: `${processingTime}ms`,
        originalSegments: originalSubtitles.length,
        translatedSegments: translatedSubtitles.length,
        hasDubbedAudio: !!dubbedAudioUrl
      });

      return result;

    } catch (error) {
      console.error('❌ Translation failed:', error);
      
      // Return fallback result
      const originalSubtitles = await this.getOriginalTranscription(sceneData);
      return {
        originalSubtitles,
        translatedSubtitles: [],
        sourceLanguage: options.sourceLanguage || 'en',
        targetLanguage: options.targetLanguage,
        confidence: 0,
        method: 'failed',
        duration: sceneData.sceneDuration,
        processingTime: Date.now() - startTime
      };
    }
  }

  // Get original transcription from pre-stored data
  private async getOriginalTranscription(
    sceneData: { analysisId: string; sceneStart: number; sceneDuration: number }
  ): Promise<SubtitleCue[]> {
    try {
      // Use pre-stored transcription if available
      if (transcriptionStorageService.hasTranscription(sceneData.analysisId)) {
        console.log('✅ Using pre-stored transcription for translation');
        return transcriptionStorageService.getSceneSubtitles(
          sceneData.analysisId,
          sceneData.sceneStart,
          sceneData.sceneDuration
        );
      }

      console.log('⚠️ No pre-stored transcription found');
      return [];

    } catch (error) {
      console.error('❌ Failed to get original transcription:', error);
      return [];
    }
  }

  // Translate subtitle text using Google Translate API
  private async translateSubtitles(
    originalSubtitles: SubtitleCue[],
    options: TranslationOptions
  ): Promise<SubtitleCue[]> {
    try {
      console.log('🔄 Translating subtitles using Google Translate API...');

      if (!this.googleApiKey) {
        console.warn('⚠️ No Google Cloud API key configured, using mock translation');
        return this.mockTranslateSubtitles(originalSubtitles, options);
      }

      // Batch translate all subtitle texts
      const textsToTranslate = originalSubtitles.map(subtitle => subtitle.text);
      const translatedTexts = await this.translateTexts(textsToTranslate, options);

      // Create translated subtitles with the same timing
      const translatedSubtitles: SubtitleCue[] = originalSubtitles.map((subtitle, index) => ({
        ...subtitle,
        id: `translated-${subtitle.id}`,
        text: translatedTexts[index] || subtitle.text // Fallback to original if translation failed
      }));

      console.log(`✅ Successfully translated ${translatedSubtitles.length} subtitle segments`);
      return translatedSubtitles;

    } catch (error) {
      console.error('❌ Subtitle translation failed:', error);
      console.log('🔄 Falling back to mock translation...');
      return this.mockTranslateSubtitles(originalSubtitles, options);
    }
  }

  // Real Google Translate API call
  private async translateTexts(
    texts: string[],
    options: TranslationOptions
  ): Promise<string[]> {
    if (!this.googleApiKey) {
      throw new Error('Google Cloud API key not configured');
    }

    try {
      const response = await fetch(`${GOOGLE_TRANSLATE_API_URL}?key=${this.googleApiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: texts,
          target: options.targetLanguage,
          source: options.sourceLanguage || 'en',
          format: 'text'
        })
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Google Translate API error: ${response.status} - ${errorData}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(`Google Translate API error: ${data.error.message}`);
      }

      // Extract translated texts from response
      const translations = data.data.translations;
      return translations.map((translation: any) => translation.translatedText);

    } catch (error) {
      console.error('❌ Google Translate API call failed:', error);
      throw error;
    }
  }

  // Fallback mock translation for when API is not available
  private mockTranslateSubtitles(
    originalSubtitles: SubtitleCue[],
    options: TranslationOptions
  ): SubtitleCue[] {
    return originalSubtitles.map(subtitle => ({
      ...subtitle,
      id: `translated-${subtitle.id}`,
      text: this.mockTranslate(subtitle.text, options.targetLanguage)
    }));
  }

  // Generate dubbed audio using Google Text-to-Speech
  private async generateDubbedAudio(
    translatedSubtitles: SubtitleCue[],
    options: TranslationOptions
  ): Promise<{ url: string; blob: Blob }> {
    try {
      console.log('🎤 Generating dubbed audio using Google TTS API...');

      if (!this.googleApiKey) {
        console.warn('⚠️ No Google Cloud API key configured, using mock audio');
        const mockAudioBlob = await this.createMockAudioBlob(translatedSubtitles, options);
        const audioUrl = URL.createObjectURL(mockAudioBlob);
        return { url: audioUrl, blob: mockAudioBlob };
      }

      // Generate audio for each subtitle segment
      const audioSegments: Blob[] = [];

      for (const subtitle of translatedSubtitles) {
        try {
          const audioBlob = await this.synthesizeSpeech(subtitle.text, options);
          audioSegments.push(audioBlob);
        } catch (error) {
          console.warn(`⚠️ Failed to synthesize speech for segment: ${subtitle.text}`, error);
          // Create silent audio segment as fallback
          const silentBlob = await this.createSilentAudioBlob(subtitle.endTime - subtitle.startTime);
          audioSegments.push(silentBlob);
        }
      }

      // Combine all audio segments into one file
      const combinedAudioBlob = await this.combineAudioSegments(audioSegments, translatedSubtitles);
      const audioUrl = URL.createObjectURL(combinedAudioBlob);

      console.log('✅ Successfully generated dubbed audio');
      return { url: audioUrl, blob: combinedAudioBlob };

    } catch (error) {
      console.error('❌ Dubbed audio generation failed:', error);
      console.log('🔄 Falling back to mock audio...');
      const mockAudioBlob = await this.createMockAudioBlob(translatedSubtitles, options);
      const audioUrl = URL.createObjectURL(mockAudioBlob);
      return { url: audioUrl, blob: mockAudioBlob };
    }
  }

  // Real Google TTS API call
  private async synthesizeSpeech(text: string, options: TranslationOptions): Promise<Blob> {
    if (!this.googleApiKey) {
      throw new Error('Google Cloud API key not configured');
    }

    try {
      const voiceConfig = TTS_VOICE_CONFIGS[options.targetLanguage] || TTS_VOICE_CONFIGS['en'];

      const response = await fetch(`${GOOGLE_TTS_API_URL}?key=${this.googleApiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: { text },
          voice: {
            languageCode: options.targetLanguage === 'zh' ? 'cmn-CN' : options.targetLanguage,
            name: voiceConfig.name,
            ssmlGender: voiceConfig.gender
          },
          audioConfig: {
            audioEncoding: 'MP3',
            speakingRate: options.speechRate || 1.0,
            pitch: 0.0,
            volumeGainDb: 0.0
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Google TTS API error: ${response.status} - ${errorData}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(`Google TTS API error: ${data.error.message}`);
      }

      // Convert base64 audio to blob
      const audioContent = data.audioContent;
      const audioBytes = Uint8Array.from(atob(audioContent), c => c.charCodeAt(0));
      return new Blob([audioBytes], { type: 'audio/mp3' });

    } catch (error) {
      console.error('❌ Google TTS API call failed:', error);
      throw error;
    }
  }

  // Combine multiple audio segments with proper timing
  private async combineAudioSegments(
    audioSegments: Blob[],
    subtitles: SubtitleCue[]
  ): Promise<Blob> {
    try {
      // For now, just concatenate the audio segments
      // In a more advanced implementation, you would add silence between segments
      // to match the subtitle timing
      const combinedArray = new Uint8Array(
        audioSegments.reduce((total, blob) => total + blob.size, 0)
      );

      let offset = 0;
      for (const blob of audioSegments) {
        const arrayBuffer = await blob.arrayBuffer();
        combinedArray.set(new Uint8Array(arrayBuffer), offset);
        offset += arrayBuffer.byteLength;
      }

      return new Blob([combinedArray], { type: 'audio/mp3' });
    } catch (error) {
      console.error('❌ Failed to combine audio segments:', error);
      // Return the first segment as fallback
      return audioSegments[0] || new Blob([], { type: 'audio/mp3' });
    }
  }

  // Create silent audio blob for fallback
  private async createSilentAudioBlob(duration: number): Promise<Blob> {
    // Create a simple silent MP3-like blob
    const silentData = new Uint8Array(Math.floor(duration * 1000)); // Rough estimate
    return new Blob([silentData], { type: 'audio/mp3' });
  }

  // Mock translation for demo (replace with real Google Translate API)
  private mockTranslate(text: string, targetLanguage: string): string {
    const mockTranslations: Record<string, Record<string, string>> = {
      'es': {
        'Hello': 'Hola',
        'world': 'mundo',
        'video': 'video',
        'editing': 'edición',
        'timeline': 'línea de tiempo'
      },
      'fr': {
        'Hello': 'Bonjour',
        'world': 'monde',
        'video': 'vidéo',
        'editing': 'montage',
        'timeline': 'chronologie'
      },
      'de': {
        'Hello': 'Hallo',
        'world': 'Welt',
        'video': 'Video',
        'editing': 'Bearbeitung',
        'timeline': 'Zeitleiste'
      }
    };

    const languageCode = LANGUAGE_CODES[targetLanguage.toLowerCase()] || targetLanguage;
    const translations = mockTranslations[languageCode];

    if (!translations) {
      return `[${languageCode.toUpperCase()}] ${text}`;
    }

    let translatedText = text;
    Object.entries(translations).forEach(([english, translated]) => {
      const regex = new RegExp(`\\b${english}\\b`, 'gi');
      translatedText = translatedText.replace(regex, translated);
    });

    return translatedText;
  }

  // Create mock audio blob for demo (replace with real TTS API)
  private async createMockAudioBlob(
    translatedSubtitles: SubtitleCue[],
    options: TranslationOptions
  ): Promise<Blob> {
    // Create a simple audio context for demo
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const duration = Math.max(...translatedSubtitles.map(s => s.endTime));
    const sampleRate = audioContext.sampleRate;
    const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
    
    // Generate simple tone for demo
    const data = buffer.getChannelData(0);
    for (let i = 0; i < data.length; i++) {
      data[i] = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 0.1;
    }

    // Convert to WAV blob
    const wavBlob = this.audioBufferToWav(buffer);
    return wavBlob;
  }

  // Convert AudioBuffer to WAV blob
  private audioBufferToWav(buffer: AudioBuffer): Blob {
    const length = buffer.length;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, buffer.sampleRate, true);
    view.setUint32(28, buffer.sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);

    // Convert float samples to 16-bit PCM
    const data = buffer.getChannelData(0);
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, data[i]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }

    return new Blob([arrayBuffer], { type: 'audio/wav' });
  }

  // Get supported languages
  getSupportedLanguages(): Array<{ code: string; name: string }> {
    return [
      { code: 'en', name: 'English' },
      { code: 'es', name: 'Spanish' },
      { code: 'fr', name: 'French' },
      { code: 'de', name: 'German' },
      { code: 'it', name: 'Italian' },
      { code: 'pt', name: 'Portuguese' },
      { code: 'ru', name: 'Russian' },
      { code: 'ja', name: 'Japanese' },
      { code: 'ko', name: 'Korean' },
      { code: 'zh', name: 'Chinese' },
      { code: 'ar', name: 'Arabic' },
      { code: 'hi', name: 'Hindi' }
    ];
  }
}

// Export singleton instance
export const translationService = new TranslationService();
export default translationService;
