// src/services/translationService.ts
// Translation service using AssemblyAI transcription + Google Translate + Google TTS

import type { SubtitleCue } from '../components/SubtitleOverlay';
import { transcriptionStorageService } from './transcriptionStorageService';
import { getApiKey } from '../config/apiKeys';

export interface TranslationOptions {
  targetLanguage: string;
  sourceLanguage?: string; // Auto-detect if not provided
  includeSubtitles: boolean;
  includeDubbedAudio: boolean;
  voiceType?: 'male' | 'female' | 'neutral';
  speechRate?: number; // 0.25 to 4.0
}

export interface TranslationResult {
  originalSubtitles: SubtitleCue[];
  translatedSubtitles: SubtitleCue[];
  dubbedAudioUrl?: string;
  dubbedAudioBlob?: Blob;
  sourceLanguage: string;
  targetLanguage: string;
  confidence: number;
  method: string;
  duration: number;
  processingTime: number;
}

// Google Translate API configuration
const GOOGLE_TRANSLATE_API_URL = 'https://translation.googleapis.com/language/translate/v2';
const GOOGLE_TTS_API_URL = 'https://texttospeech.googleapis.com/v1/text:synthesize';

// Language mappings for Google services
const LANGUAGE_CODES = {
  'english': 'en',
  'spanish': 'es',
  'french': 'fr',
  'german': 'de',
  'italian': 'it',
  'portuguese': 'pt',
  'russian': 'ru',
  'japanese': 'ja',
  'korean': 'ko',
  'chinese': 'zh',
  'arabic': 'ar',
  'hindi': 'hi'
};

const TTS_VOICE_CONFIGS = {
  'en': { name: 'en-US-Wavenet-D', gender: 'MALE' },
  'es': { name: 'es-ES-Wavenet-B', gender: 'MALE' },
  'fr': { name: 'fr-FR-Wavenet-B', gender: 'MALE' },
  'de': { name: 'de-DE-Wavenet-B', gender: 'MALE' },
  'it': { name: 'it-IT-Wavenet-A', gender: 'FEMALE' },
  'pt': { name: 'pt-BR-Wavenet-A', gender: 'FEMALE' },
  'ru': { name: 'ru-RU-Wavenet-A', gender: 'FEMALE' },
  'ja': { name: 'ja-JP-Wavenet-A', gender: 'FEMALE' },
  'ko': { name: 'ko-KR-Wavenet-A', gender: 'FEMALE' },
  'zh': { name: 'cmn-CN-Wavenet-A', gender: 'FEMALE' },
  'ar': { name: 'ar-XA-Wavenet-A', gender: 'FEMALE' },
  'hi': { name: 'hi-IN-Wavenet-A', gender: 'FEMALE' }
};

class TranslationService {
  private googleApiKey: string | null = null;

  constructor() {
    // For now, we'll use a demo key or require user to provide their own
    // In production, this should be configured properly
    this.googleApiKey = getApiKey('google-translate') || null;
  }

  // Main translation method
  async translateScene(
    sceneData: { analysisId: string; sceneStart: number; sceneDuration: number },
    options: TranslationOptions
  ): Promise<TranslationResult> {
    const startTime = Date.now();
    
    try {
      console.log('🌐 Starting translation process...', { sceneData, options });

      // Step 1: Get original transcription (instant from pre-stored data)
      const originalSubtitles = await this.getOriginalTranscription(sceneData);
      
      if (originalSubtitles.length === 0) {
        throw new Error('No transcription available for this scene');
      }

      console.log(`📝 Found ${originalSubtitles.length} original subtitle segments`);

      // Step 2: Translate text
      let translatedSubtitles: SubtitleCue[] = [];
      if (options.includeSubtitles) {
        translatedSubtitles = await this.translateSubtitles(originalSubtitles, options);
        console.log(`🔄 Translated ${translatedSubtitles.length} subtitle segments`);
      }

      // Step 3: Generate dubbed audio
      let dubbedAudioUrl: string | undefined;
      let dubbedAudioBlob: Blob | undefined;
      if (options.includeDubbedAudio && translatedSubtitles.length > 0) {
        const audioResult = await this.generateDubbedAudio(translatedSubtitles, options);
        dubbedAudioUrl = audioResult.url;
        dubbedAudioBlob = audioResult.blob;
        console.log('🎤 Generated dubbed audio');
      }

      const processingTime = Date.now() - startTime;

      const result: TranslationResult = {
        originalSubtitles,
        translatedSubtitles,
        dubbedAudioUrl,
        dubbedAudioBlob,
        sourceLanguage: options.sourceLanguage || 'en',
        targetLanguage: options.targetLanguage,
        confidence: 0.9, // High confidence for API-based translation
        method: 'google-translate-tts',
        duration: sceneData.sceneDuration,
        processingTime
      };

      console.log('✅ Translation completed successfully', {
        processingTime: `${processingTime}ms`,
        originalSegments: originalSubtitles.length,
        translatedSegments: translatedSubtitles.length,
        hasDubbedAudio: !!dubbedAudioUrl
      });

      return result;

    } catch (error) {
      console.error('❌ Translation failed:', error);
      
      // Return fallback result
      const originalSubtitles = await this.getOriginalTranscription(sceneData);
      return {
        originalSubtitles,
        translatedSubtitles: [],
        sourceLanguage: options.sourceLanguage || 'en',
        targetLanguage: options.targetLanguage,
        confidence: 0,
        method: 'failed',
        duration: sceneData.sceneDuration,
        processingTime: Date.now() - startTime
      };
    }
  }

  // Get original transcription from pre-stored data
  private async getOriginalTranscription(
    sceneData: { analysisId: string; sceneStart: number; sceneDuration: number }
  ): Promise<SubtitleCue[]> {
    try {
      // Use pre-stored transcription if available
      if (transcriptionStorageService.hasTranscription(sceneData.analysisId)) {
        console.log('✅ Using pre-stored transcription for translation');
        return transcriptionStorageService.getSceneSubtitles(
          sceneData.analysisId,
          sceneData.sceneStart,
          sceneData.sceneDuration
        );
      }

      console.log('⚠️ No pre-stored transcription found');
      return [];

    } catch (error) {
      console.error('❌ Failed to get original transcription:', error);
      return [];
    }
  }

  // Translate subtitle text using Google Translate API
  private async translateSubtitles(
    originalSubtitles: SubtitleCue[],
    options: TranslationOptions
  ): Promise<SubtitleCue[]> {
    try {
      // For demo purposes, we'll use a mock translation
      // In production, this would call Google Translate API
      console.log('🔄 Translating subtitles (using mock translation for demo)...');

      const translatedSubtitles: SubtitleCue[] = originalSubtitles.map(subtitle => ({
        ...subtitle,
        id: `translated-${subtitle.id}`,
        text: this.mockTranslate(subtitle.text, options.targetLanguage)
      }));

      return translatedSubtitles;

    } catch (error) {
      console.error('❌ Subtitle translation failed:', error);
      return [];
    }
  }

  // Generate dubbed audio using Google Text-to-Speech
  private async generateDubbedAudio(
    translatedSubtitles: SubtitleCue[],
    options: TranslationOptions
  ): Promise<{ url: string; blob: Blob }> {
    try {
      console.log('🎤 Generating dubbed audio (using mock audio for demo)...');

      // For demo purposes, create a mock audio blob
      // In production, this would call Google TTS API
      const mockAudioBlob = await this.createMockAudioBlob(translatedSubtitles, options);
      const audioUrl = URL.createObjectURL(mockAudioBlob);

      return { url: audioUrl, blob: mockAudioBlob };

    } catch (error) {
      console.error('❌ Dubbed audio generation failed:', error);
      throw error;
    }
  }

  // Mock translation for demo (replace with real Google Translate API)
  private mockTranslate(text: string, targetLanguage: string): string {
    const mockTranslations: Record<string, Record<string, string>> = {
      'es': {
        'Hello': 'Hola',
        'world': 'mundo',
        'video': 'video',
        'editing': 'edición',
        'timeline': 'línea de tiempo'
      },
      'fr': {
        'Hello': 'Bonjour',
        'world': 'monde',
        'video': 'vidéo',
        'editing': 'montage',
        'timeline': 'chronologie'
      },
      'de': {
        'Hello': 'Hallo',
        'world': 'Welt',
        'video': 'Video',
        'editing': 'Bearbeitung',
        'timeline': 'Zeitleiste'
      }
    };

    const languageCode = LANGUAGE_CODES[targetLanguage.toLowerCase()] || targetLanguage;
    const translations = mockTranslations[languageCode];

    if (!translations) {
      return `[${languageCode.toUpperCase()}] ${text}`;
    }

    let translatedText = text;
    Object.entries(translations).forEach(([english, translated]) => {
      const regex = new RegExp(`\\b${english}\\b`, 'gi');
      translatedText = translatedText.replace(regex, translated);
    });

    return translatedText;
  }

  // Create mock audio blob for demo (replace with real TTS API)
  private async createMockAudioBlob(
    translatedSubtitles: SubtitleCue[],
    options: TranslationOptions
  ): Promise<Blob> {
    // Create a simple audio context for demo
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const duration = Math.max(...translatedSubtitles.map(s => s.endTime));
    const sampleRate = audioContext.sampleRate;
    const buffer = audioContext.createBuffer(1, duration * sampleRate, sampleRate);
    
    // Generate simple tone for demo
    const data = buffer.getChannelData(0);
    for (let i = 0; i < data.length; i++) {
      data[i] = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 0.1;
    }

    // Convert to WAV blob
    const wavBlob = this.audioBufferToWav(buffer);
    return wavBlob;
  }

  // Convert AudioBuffer to WAV blob
  private audioBufferToWav(buffer: AudioBuffer): Blob {
    const length = buffer.length;
    const arrayBuffer = new ArrayBuffer(44 + length * 2);
    const view = new DataView(arrayBuffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, buffer.sampleRate, true);
    view.setUint32(28, buffer.sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * 2, true);

    // Convert float samples to 16-bit PCM
    const data = buffer.getChannelData(0);
    let offset = 44;
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, data[i]));
      view.setInt16(offset, sample * 0x7FFF, true);
      offset += 2;
    }

    return new Blob([arrayBuffer], { type: 'audio/wav' });
  }

  // Get supported languages
  getSupportedLanguages(): Array<{ code: string; name: string }> {
    return [
      { code: 'en', name: 'English' },
      { code: 'es', name: 'Spanish' },
      { code: 'fr', name: 'French' },
      { code: 'de', name: 'German' },
      { code: 'it', name: 'Italian' },
      { code: 'pt', name: 'Portuguese' },
      { code: 'ru', name: 'Russian' },
      { code: 'ja', name: 'Japanese' },
      { code: 'ko', name: 'Korean' },
      { code: 'zh', name: 'Chinese' },
      { code: 'ar', name: 'Arabic' },
      { code: 'hi', name: 'Hindi' }
    ];
  }
}

// Export singleton instance
export const translationService = new TranslationService();
export default translationService;
